package com.wexl.retail.thread.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.model.ThreadReply;
import com.wexl.retail.thread.model.ThreadReplyComments;
import com.wexl.retail.thread.repository.ThreadReplyCommentsRepository;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ThreadReplyCommentsService {
  private final StudentService studentService;
  private final TeacherService teacherService;
  private final ThreadReplyService threadReplyService;
  private final ThreadReplyCommentsRepository threadReplyCommentsRepository;
  private final UserRepository userRepository;
  private static final String ERROR_THREAD_REPLY_COMMENT = "error.ThreadReplyComment";
  private final NotificationsService notificationsService;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;

  public void createThreadReplyCommentsByTeacher(
      ThreadDto.CreateThreadReplyRequest request,
      Long threadReplyId,
      String teacherAuthId,
      String orgSlug) {
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    var threadReply = threadReplyService.findThreadReplyById(threadReplyId);

    // Get only one admin instead of all admins to avoid duplicate notifications
//    User adminUser = userRepository.getOrgAdminForOrganization(orgSlug);
//
//    List<String> authUserIds = new ArrayList<>();
//    authUserIds.add(threadReply.getThread().getAssignedTo().getUserInfo().getAuthUserId());
//    authUserIds.add(adminUser.getAuthUserId());
//
//    for (String authId : authUserIds) {
//      var notificationRequest =
//          NotificationDto.NotificationRequest.builder()
//              .title("New Thread Reply Comment")
//              .message(request.reply())
//              .notificationType(NotificationType.FORUM)
//              .userAuthId(authId)
//              .build();
//      notificationsService.createNotificationByTeacher(
//          threadReply.getThread().getOrgSlug(), notificationRequest, teacher.getUserInfo().getAuthUserId(), false);
//      eventNotificationService.sendPushNotificationForUser(
//          authId,
//          threadReply.getThread().getOrgSlug(),
//          request.reply(),
//          NotificationType.FORUM,
//          "Thread Reply Comment Notification");
//    }
    threadReplyCommentsRepository.save(buildTeacherRequest(request, teacher, threadReply));
  }

  private ThreadReplyComments buildTeacherRequest(
      ThreadDto.CreateThreadReplyRequest request, Teacher teacher, ThreadReply threadReply) {
    ThreadReplyComments parentData = null;
    if (request.threadReplyCommentId() != null) {
      parentData = findThreadReplyCommentId(request.threadReplyCommentId());
    }
    return ThreadReplyComments.builder()
        .isTeacher(true)
        .isStudent(false)
        .parent(parentData)
        .threadReplies(threadReply)
        .userInfo(teacher.getUserInfo())
        .reply(request.reply())
        .build();
  }

  public void createThreadReplyCommentsByStudent(
      ThreadDto.CreateThreadReplyRequest request, Long threadReplyId, String studentAuthId) {
    var student = studentService.getStudentByAuthId(studentAuthId);
    var threadReply = threadReplyService.findThreadReplyById(threadReplyId);
    threadReplyCommentsRepository.save(buildStudentRequest(request, student, threadReply));
  }

  private ThreadReplyComments buildStudentRequest(
      ThreadDto.CreateThreadReplyRequest request, Student student, ThreadReply threadReply) {
    ThreadReplyComments parentData = null;
    if (request.threadReplyCommentId() != null) {
      parentData = findThreadReplyCommentId(request.threadReplyCommentId());
    }
    return ThreadReplyComments.builder()
        .isTeacher(false)
        .isStudent(true)
        .parent(parentData)
        .threadReplies(threadReply)
        .reply(request.reply())
        .userInfo(student.getUserInfo())
        .build();
  }

  public ThreadReplyComments findThreadReplyCommentId(Long id) {
    var threadReplyComments = threadReplyCommentsRepository.findById(id);
    if (threadReplyComments.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          ERROR_THREAD_REPLY_COMMENT,
          new String[] {id.toString()});
    }
    return threadReplyComments.get();
  }

  public void editThreadReplyCommentsByTeacher(
      ThreadDto.CreateThreadReplyRequest request, String teacherAuthId, Long threadReplyCommentId) {
    var user =
        userRepository
            .findByAuthUserId(teacherAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var threadReplyComment =
        threadReplyCommentsRepository
            .findByIdAndUserInfo(threadReplyCommentId, user)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        ERROR_THREAD_REPLY_COMMENT,
                        new String[] {threadReplyCommentId.toString()}));
    editThreadReplyComment(threadReplyComment, request);
  }

  private void editThreadReplyComment(
      ThreadReplyComments threadReplyComment, ThreadDto.CreateThreadReplyRequest request) {
    threadReplyComment.setReply(request.reply());
    threadReplyComment.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    threadReplyCommentsRepository.save(threadReplyComment);
  }

  public void editThreadReplyCommentsByStudent(
      ThreadDto.CreateThreadReplyRequest request, String studentAuthId, Long threadReplyCommentId) {
    var user =
        userRepository
            .findByAuthUserId(studentAuthId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var threadReplyComment =
        threadReplyCommentsRepository
            .findByIdAndUserInfo(threadReplyCommentId, user)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        ERROR_THREAD_REPLY_COMMENT,
                        new String[] {threadReplyCommentId.toString()}));
    editThreadReplyComment(threadReplyComment, request);
  }
}
