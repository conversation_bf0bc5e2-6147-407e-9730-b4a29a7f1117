package com.wexl.retail.thread.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.organization.admin.teacher.TeacherService;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.model.Thread;
import com.wexl.retail.thread.model.ThreadReply;
import com.wexl.retail.thread.repository.ThreadReplyRepository;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ThreadReplyService {

  private final TeacherService teacherService;
  private final ThreadReplyRepository threadReplyRepository;
  private final ThreadService threadService;
  private final NotificationsService notificationsService;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;
  private final UserRepository userRepository;

  public void createThreadReply(
      ThreadDto.CreateThreadReplyRequest request, String teacherAuthId, Long threadId) {
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    var thread = threadService.findThreadById(threadId);

    var student = thread.getCreatedBy();

    // Get only one admin instead of all admins to avoid duplicate notifications
    User adminUser = userRepository.getOrgAdminForOrganization(thread.getOrgSlug());

    // Create notification for student
    var studentNotificationRequest =
            NotificationDto.NotificationRequest.builder()
                    .title("New Thread Reply")
                    .message(request.reply())
                    .notificationType(NotificationType.FORUM)
                    .userAuthId(student.getUserInfo().getAuthUserId())
                    .build();

    notificationsService.createNotificationByUser(
            thread.getOrgSlug(), studentNotificationRequest, student.getUserInfo().getAuthUserId());

    // Create notification for admin
    var adminNotificationRequest =
            NotificationDto.NotificationRequest.builder()
                    .title("New Thread Reply")
                    .message(request.reply())
                    .notificationType(NotificationType.FORUM)
                    .userAuthId(adminUser.getAuthUserId())
                    .build();

    notificationsService.createNotificationByUser(
            thread.getOrgSlug(), adminNotificationRequest, adminUser.getAuthUserId());

    // Send push notifications to both users
    eventNotificationService.sendPushNotificationForUser(
            student.getUserInfo().getAuthUserId(),
            request.reply(),
            thread.getOrgSlug(),
            NotificationType.FORUM,
            "Thread Reply Notification");

    eventNotificationService.sendPushNotificationForUser(
            adminUser.getAuthUserId(),
            request.reply(),
            thread.getOrgSlug(),
            NotificationType.FORUM,
            "Thread Reply Notification");

    threadReplyRepository.save(buildThreadRequest(request, thread, teacher));
  }

  private ThreadReply buildThreadRequest(
      ThreadDto.CreateThreadReplyRequest request, Thread thread, Teacher teacher) {
    return ThreadReply.builder().reply(request.reply()).teacher(teacher).thread(thread).build();
  }

  public ThreadReply findThreadReplyById(Long threadReplyId) {
    var threadReply = threadReplyRepository.findById(threadReplyId);
    if (threadReply.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.ThreadReplyId",
          new String[] {threadReplyId.toString()});
    }
    return threadReply.get();
  }

  public void editThreadReply(
      Long threadReplyId, String teacherAuthId, ThreadDto.CreateThreadReplyRequest request) {
    var teacher = teacherService.getTeacherByAuthId(teacherAuthId);
    var threadReply =
        threadReplyRepository
            .findByIdAndTeacher(threadReplyId, teacher)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ThreadReplyId"));
    threadReply.setReply(request.reply());
    threadReply.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    threadReplyRepository.save(threadReply);
  }
}
